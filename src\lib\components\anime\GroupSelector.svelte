<script>
	import { createEventDispatcher, onMount } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import { userStore } from '$lib/stores/userLogin';
	import { Dialog, DialogContent, DialogHeader, Di<PERSON>Title, DialogFooter } from '$lib/components/ui/dialog';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import { toast } from 'svelte-sonner';
	import { fade, scale, slide } from 'svelte/transition';
	import { isAdmin as checkIsAdmin } from '$lib/utils/clientRoleUtils';

	export let otherGroups = null; // For backward compatibility
	export let episodeId = null;
	export let selectedGroup = null;
	export let episode = null; // Add episode prop to access secondarySource and new fields

	// New data structure
	let translatingGroups = [];
	let playerSources = [];

	const dispatch = createEventDispatcher();

	// Initialize isAdmin variable
	let isAdmin = false;

	// Force admin check on component mount
	onMount(async () => {
		checkAdminStatus();
		await fetchTranslatingGroupsAndPlayerSources();
	});

	// Function to fetch translating groups and player sources
	async function fetchTranslatingGroupsAndPlayerSources() {
		try {
			const response = await fetch('/api/groups');

			if (!response.ok) {
				throw new Error('Failed to fetch translating groups and player sources');
			}

			const data = await response.json();
			translatingGroups = data.translatingGroups || [];
			playerSources = data.playerSources || [];

			console.log('Fetched translating groups:', translatingGroups);
			console.log('Fetched player sources:', playerSources);
		} catch (error) {
			console.error('Error fetching translating groups and player sources:', error);
			toast.error('Nie udało się pobrać grup tłumaczeniowych i źródeł odtwarzaczy');
		}
	}

	// Function to check admin status
	function checkAdminStatus() {
		// console.log('Checking admin status in GroupSelector');
		// console.log('User store:', $userStore);
		// console.log('User metadata:', $userStore?.user_metadata);
		// console.log('User profile:', $userStore?.user_metadata?.profile);

		// Check if user has admin role in user_metadata.profile
		const hasAdminRole = $userStore?.user_metadata?.profile?.role === 'admin';
		// console.log('Has admin role:', hasAdminRole);

		// Update isAdmin
		isAdmin = hasAdminRole;
		// console.log('isAdmin set to:', isAdmin);
	}

	// Watch for changes in the user store
	$: if ($userStore) {
		checkAdminStatus();
	}

	let editMode = false;
	let showAddPlayerDialog = false;
	let showAddGroupDialog = false;
	let showDeleteConfirmDialog = false;
	let showPlayerSelectDialog = false;
	let hasSecondarySource = false;
	let allGroups = {};
	let newPlayerUrl = '';
	let selectedGroupForNewPlayer = '';
	let selectedGroupFromList = '';
	let playerToDelete = { groupName: '', playerUrl: '' };

	// New player options
	let selectedQuality = 'HD';
	let selectedAudioLanguage = 'jp';
	let selectedSubtitleLanguage = 'pl';
	let deleteType = 'player'; // Only deleting players now
	let currentGroupPlayers = [];
	let currentPlayerType = null; // Track the current player type
	let selectedGroupUrl = null; // Track the selected player URL
	let availableGroups = []; // List of groups from translating_groups table

	// Function to preserve original player name casing
	function preservePlayerName(name) {
		if (!name) return name;
		return name; // Return original casing
	}

	// Function to get sorted group entries with lycoris.cafe first, then by date of first player (oldest first)
	function getSortedGroupEntries(groups) {
		if (!groups) return [];

		const entries = Object.entries(groups);

		// Sort to ensure lycoris.cafe is always first, then by date of adding first player (oldest first)
		return entries.sort(([a, groupAData], [b, groupBData]) => {
			if (a === 'lycoris.cafe') return -1;
			if (b === 'lycoris.cafe') return 1;

			// For other groups, sort by date_added of first player (oldest first)
			// Get the earliest date_added from all players in each group (first player added)
			const getFirstPlayerDate = (groupData) => {
				if (!groupData?.players || groupData.players.length === 0) return '9999-12-31'; // Far future for groups with no players

				// Find the earliest date among all players (first player added to this group)
				const dates = groupData.players
					.map((player) => player.date_added)
					.filter((date) => date) // Remove null/undefined dates
					.sort(); // Sort dates ascending (earliest first)

				return dates[0] || '9999-12-31'; // Return earliest date (first player) or far future fallback
			};

			const firstPlayerDateA = getFirstPlayerDate(groupAData);
			const firstPlayerDateB = getFirstPlayerDate(groupBData);

			// Sort by date (oldest first - groups that added their first player earliest appear first)
			// So okami-subs (2025-05-18) appears before wbijam.pl (2025-06-09)
			return new Date(firstPlayerDateA) - new Date(firstPlayerDateB);
		});
	}

	// Since we're using direct embedding, we don't need to check if a player is supported anymore
	// All players are considered supported

	// Check if the episode has a secondary source (lycoris.cafe)
	function checkSecondarySource() {
		if (episode && episode.secondarySource && Object.keys(episode.secondarySource).length > 0) {
			hasSecondarySource = true;
			return true;
		}
		return false;
	}

	// Create a combined groups object with lycoris.cafe first and new data structure
	function createGroupsList() {
		// Save current players for the selected group before updating allGroups
		const savedCurrentGroupPlayers = [...currentGroupPlayers];

		// Start with an empty object
		allGroups = {};

		console.log('=== GROUPS LIST CREATION DEBUG ===');
		console.log('Episode data:', episode);
		console.log('Episode allGroups:', episode?.allGroups);
		console.log('Episode secondarySource:', episode?.secondarySource);
		console.log('Episode translating_group:', episode?.translating_group);
		console.log('Episode external_player_link:', episode?.external_player_link);
		console.log('Episode player_source:', episode?.player_source);

		// First, check if we have the new allGroups data from the API
		if (episode && episode.allGroups && Object.keys(episode.allGroups).length > 0) {
			// Use the new allGroups data directly from the API
			allGroups = { ...episode.allGroups };
			console.log('Using allGroups from API:', allGroups);
		} else {
			// Fallback to the old method for backward compatibility
			console.log('Using fallback method to create groups');

			// Add lycoris.cafe group if secondary source exists
			if (episode && episode.secondarySource && Object.keys(episode.secondarySource).length > 0) {
				allGroups['lycoris.cafe'] = {
					players: [{ 'lycoris.cafe': episode.secondarySource.FHD || episode.secondarySource.HD || episode.secondarySource.SD }],
					logo_url: 'https://lycoris.cafe/logo.png',
					quality: 'HD',
					audio_language: episode.audio_language || 'jp',
					subtitle_language: episode.subtitle_language || 'pl'
				};
				console.log('Added lycoris.cafe group:', allGroups['lycoris.cafe']);
			}

			// Add group from episode's translating_group if it exists and is not lycoris_cafe
			if (episode && episode.translating_group && episode.translating_group !== 'lycoris_cafe' && episode.external_player_link) {
				// Find the translating group in our fetched list
				const groupInfo = translatingGroups.find((g) => g.name === episode.translating_group);

				if (groupInfo) {
					allGroups[episode.translating_group] = {
						players: [{ [episode.player_source || 'external']: episode.external_player_link }],
						logo_url: groupInfo.logo_url || 'https://lycoris.cafe/logo.png',
						quality: episode.quality || 'HD',
						audio_language: episode.audio_language || 'jp',
						subtitle_language: episode.subtitle_language || 'pl'
					};
					console.log(`Added ${episode.translating_group} group:`, allGroups[episode.translating_group]);
				}
			}
		}

		console.log('Final allGroups:', allGroups);
		console.log('=== END GROUPS LIST CREATION DEBUG ===');

		// For backward compatibility, add other groups if they exist
		if (otherGroups) {
			// Merge with existing groups, but don't overwrite
			Object.keys(otherGroups).forEach((groupName) => {
				if (!allGroups[groupName]) {
					allGroups[groupName] = otherGroups[groupName];
				}
			});
		}

		// Update player information if a group is already selected
		if (selectedGroup && allGroups[selectedGroup] && allGroups[selectedGroup].players && allGroups[selectedGroup].players.length > 0) {
			const bestPlayer = getBestPlayer(allGroups[selectedGroup], selectedGroup);
			// All players are now considered supported

			// Set the current player type
			if (bestPlayer && bestPlayer.type) {
				currentPlayerType = bestPlayer.type;
			} else if (selectedGroup === 'lycoris.cafe') {
				currentPlayerType = 'lycoris.cafe';
			}

			// Restore the saved players for the current group
			if (savedCurrentGroupPlayers.length > 0) {
				currentGroupPlayers = savedCurrentGroupPlayers;
				console.log('Restored currentGroupPlayers in createGroupsList:', currentGroupPlayers);
			} else if (allGroups[selectedGroup].players.length > 0) {
				// If we didn't have saved players but the group has players, update currentGroupPlayers
				currentGroupPlayers = [...allGroups[selectedGroup].players];
				console.log('Updated currentGroupPlayers in createGroupsList:', currentGroupPlayers);
			}
		}
	}

	// Fetch available groups from translating_groups table
	async function fetchAvailableGroups() {
		try {
			const response = await fetch('/api/translating-groups');
			if (response.ok) {
				const data = await response.json();
				availableGroups = data.groups || [];
			}
		} catch (error) {
			console.error('Error fetching available groups:', error);
		}
	}

	// Create the groups list but don't pre-select lycoris.cafe
	onMount(() => {
		// Always create the groups list to handle both new and old data structures
		createGroupsList();

		// Fetch available groups for edit mode
		if (isAdmin) {
			fetchAvailableGroups();
		}

		// Set default player type for lycoris.cafe if it's selected
		if (selectedGroup === 'lycoris.cafe') {
			currentPlayerType = 'lycoris.cafe';
		}

		// If no player type is set but a group is selected, set a default
		if (!currentPlayerType && selectedGroup) {
			if (selectedGroup === 'lycoris.cafe') {
				currentPlayerType = 'lycoris.cafe';
			} else if (allGroups[selectedGroup]?.players?.length > 0) {
				const firstPlayer = allGroups[selectedGroup].players[0];
				currentPlayerType = Object.keys(firstPlayer)[0];
			}
		}
	});

	function selectGroup(groupName) {
		console.log('=== GROUP SELECTOR DEBUG ===');
		console.log('Selecting group:', groupName);
		console.log('Available groups:', allGroups);
		console.log('Group data for', groupName, ':', allGroups[groupName]);

		selectedGroup = groupName;

		// Exit edit mode when a group is selected
		if (editMode) {
			editMode = false;
		}

		// Store all players for the current group for the player selection dialog
		if (groupName && allGroups[groupName] && allGroups[groupName].players) {
			currentGroupPlayers = [...allGroups[groupName].players]; // Create a new array to trigger reactivity
			console.log('Setting currentGroupPlayers in selectGroup:', currentGroupPlayers);
		} else {
			currentGroupPlayers = [];
		}

		// Get the best player from the selected group
		let playerInfo = null;

		if (groupName && allGroups[groupName] && allGroups[groupName].players && allGroups[groupName].players.length > 0) {
			const bestPlayer = getBestPlayer(allGroups[groupName], groupName);
			console.log('Best player for', groupName, ':', bestPlayer);

			playerInfo = {
				type: bestPlayer.type,
				url: bestPlayer.url
			};

			// Set the current player type
			currentPlayerType = bestPlayer.type;
			console.log('Current player type set to:', currentPlayerType);
		}

		// All players are now considered supported since we're using direct embedding

		console.log('Dispatching select event with group:', groupName, 'playerInfo:', playerInfo);
		dispatch('select', {
			group: groupName,
			playerInfo
		});

		// Automatically collapse the group list after selection
		isCollapsed = true;
		console.log('=== END GROUP SELECTOR DEBUG ===');
	}

	// Function to select a specific player from the current group
	function selectSpecificPlayer(playerType, playerUrl, groupName = null) {
		const playerInfo = {
			type: playerType,
			url: playerUrl
		};

		// Update the current player type and selected URL
		currentPlayerType = playerType;
		selectedGroupUrl = playerUrl;

		// If groupName is provided, update selectedGroup
		if (groupName) {
			selectedGroup = groupName;
		}

		// All players are now considered supported since we're using direct embedding

		dispatch('select', {
			group: groupName || selectedGroup,
			playerInfo
		});

		// Close the player selection dialog
		showPlayerSelectDialog = false;


	}

	// This function is kept for backward compatibility but is no longer needed
	// since all players are now considered supported with direct embedding
	function isPlayerSupported(playerType, groupName = null) {
		return true;
	}



	function toggleEditMode() {
		// If a group is selected, exit edit mode
		if (selectedGroup) {
			editMode = false;
			return;
		}

		// Toggle edit mode
		editMode = !editMode;
	}

	// New function to validate player URL against approved player sources
	async function validatePlayerUrl(url) {
		try {
			const response = await fetch('/api/validate-player-url', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ url })
			});

			if (!response.ok) {
				throw new Error('Failed to validate player URL');
			}

			const { isValid, playerSource } = await response.json();
			return { isValid, playerSource };
		} catch (error) {
			console.error('Error validating player URL:', error);
			return { isValid: false, playerSource: null };
		}
	}

	function openAddPlayerDialog(groupName) {
		selectedGroupForNewPlayer = groupName;
		newPlayerUrl = '';
		selectedQuality = 'HD';
		selectedAudioLanguage = 'jp';
		selectedSubtitleLanguage = 'pl';
		showAddPlayerDialog = true;
	}

	function openAddGroupDialog() {
		selectedGroupFromList = '';
		newPlayerUrl = '';
		selectedQuality = 'HD';
		selectedAudioLanguage = 'jp';
		selectedSubtitleLanguage = 'pl';
		showAddGroupDialog = true;
	}

	async function addNewPlayer() {
		if (!newPlayerUrl.trim()) {
			toast.error('URL playera nie może być pusty');
			return;
		}

		if (!selectedGroupForNewPlayer) {
			toast.error('Grupa nie została wybrana');
			return;
		}

		try {
			// Validate the player URL against approved player sources
			const { isValid, playerSource } = await validatePlayerUrl(newPlayerUrl.trim());

			if (!isValid) {
				toast.error('URL playera nie jest obsługiwany. Sprawdź czy link jest poprawny i czy player jest na liście zatwierdzonych.');
				return;
			}

			// Create new episode entry in anime_new table
			const response = await fetch('/api/anime/add-episode-player', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					episodeId,
					translatingGroup: selectedGroupForNewPlayer,
					playerSource: playerSource,
					externalPlayerLink: newPlayerUrl.trim(),
					quality: selectedQuality,
					audioLanguage: selectedAudioLanguage,
					subtitleLanguage: selectedSubtitleLanguage
				})
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Failed to add player');
			}

			const result = await response.json();
			toast.success('Player został dodany');

			// Update the local allGroups data instead of refreshing
			if (!allGroups[selectedGroupForNewPlayer]) {
				allGroups[selectedGroupForNewPlayer] = {
					players: [],
					logo_url: 'https://lycoris.cafe/logo.png',
					quality: selectedQuality,
					audio_language: selectedAudioLanguage,
					subtitle_language: selectedSubtitleLanguage
				};
			}

			// Add the new player to the group's players array
			allGroups[selectedGroupForNewPlayer].players.push({
				[playerSource]: newPlayerUrl.trim(),
				quality: selectedQuality,
				audio_language: selectedAudioLanguage,
				subtitle_language: selectedSubtitleLanguage
			});

			// Trigger reactivity
			allGroups = { ...allGroups };

			// Dispatch event to update parent component
			dispatch('groupsUpdated', { allGroups });

			newPlayerUrl = '';
			showAddPlayerDialog = false;
		} catch (error) {
			console.error('Error adding player:', error);
			toast.error(error.message || 'Nie udało się dodać playera');
		}
	}

	async function addGroupWithPlayer() {
		if (!selectedGroupFromList) {
			toast.error('Grupa nie została wybrana');
			return;
		}

		if (!newPlayerUrl.trim()) {
			toast.error('URL playera nie może być pusty');
			return;
		}

		try {
			// Validate the player URL against approved player sources
			const { isValid, playerSource } = await validatePlayerUrl(newPlayerUrl.trim());

			if (!isValid) {
				toast.error('URL playera nie jest obsługiwany. Sprawdź czy link jest poprawny i czy player jest na liście zatwierdzonych.');
				return;
			}

			// Create new episode entry in anime_new table
			const response = await fetch('/api/anime/add-episode-player', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					episodeId,
					translatingGroup: selectedGroupFromList,
					playerSource: playerSource,
					externalPlayerLink: newPlayerUrl.trim(),
					quality: selectedQuality,
					audioLanguage: selectedAudioLanguage,
					subtitleLanguage: selectedSubtitleLanguage
				})
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Failed to add group with player');
			}

			const result = await response.json();
			toast.success('Grupa z playerem została dodana');

			// Update the local allGroups data instead of refreshing
			if (!allGroups[selectedGroupFromList]) {
				allGroups[selectedGroupFromList] = {
					players: [],
					logo_url: 'https://lycoris.cafe/logo.png',
					quality: selectedQuality,
					audio_language: selectedAudioLanguage,
					subtitle_language: selectedSubtitleLanguage
				};
			}

			// Add the new player to the group's players array
			allGroups[selectedGroupFromList].players.push({
				[playerSource]: newPlayerUrl.trim(),
				quality: selectedQuality,
				audio_language: selectedAudioLanguage,
				subtitle_language: selectedSubtitleLanguage
			});

			// Trigger reactivity
			allGroups = { ...allGroups };

			// Dispatch event to update parent component
			dispatch('groupsUpdated', { allGroups });

			selectedGroupFromList = '';
			newPlayerUrl = '';
			showAddGroupDialog = false;
		} catch (error) {
			console.error('Error adding group with player:', error);
			toast.error(error.message || 'Nie udało się dodać grupy z playerem');
		}
	}

	function confirmDeletePlayer(groupName, playerUrl) {
		playerToDelete = { groupName, playerUrl };
		showDeleteConfirmDialog = true;
	}

	async function executeDelete() {
		if (playerToDelete.groupName && playerToDelete.playerUrl) {
			try {
				// Delete the episode entry from anime_new table
				const response = await fetch('/api/anime/delete-episode-player', {
					method: 'DELETE',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						episodeId,
						translatingGroup: playerToDelete.groupName,
						externalPlayerLink: playerToDelete.playerUrl
					})
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.message || 'Failed to delete player');
				}

				toast.success('Player został usunięty');

				// Update the local allGroups data instead of refreshing
				if (allGroups[playerToDelete.groupName]) {
					// Remove the player from the group's players array
					allGroups[playerToDelete.groupName].players = allGroups[playerToDelete.groupName].players.filter((player) => {
						const playerType = Object.keys(player).find((key) => key !== 'quality' && key !== 'audio_language' && key !== 'subtitle_language');
						return player[playerType] !== playerToDelete.playerUrl;
					});

					// If no players left, remove the group entirely
					if (allGroups[playerToDelete.groupName].players.length === 0) {
						delete allGroups[playerToDelete.groupName];
					}

					// Trigger reactivity
					allGroups = { ...allGroups };

					// Dispatch event to update parent component
					dispatch('groupsUpdated', { allGroups });
				}

				// If the currently selected player was removed, reset selection
				if (selectedGroup === playerToDelete.groupName && selectedGroupUrl === playerToDelete.playerUrl) {
					selectedGroup = null;
					selectedGroupUrl = null;
					selectedPlayerType = null;
					dispatch('select', {
						group: null,
						playerInfo: null
					});
				}
			} catch (error) {
				console.error('Error deleting player:', error);
				toast.error(error.message || 'Nie udało się usunąć playera');
			}
		}

		// Reset delete state
		playerToDelete = { groupName: '', playerUrl: '' };
		showDeleteConfirmDialog = false;
	}

	// Get the first player from a group
	function getBestPlayer(group, groupName = null) {
		if (!group || !group.players || !group.players.length) return null;

		// Get the first player from the group
		const firstPlayer = group.players[0];
		const playerType = Object.keys(firstPlayer)[0];
		return {
			type: playerType,
			url: firstPlayer[playerType]
		};
	}

	function getFirstPlayerUrl(group) {
		if (group && group.players && group.players.length > 0) {
			const firstPlayer = group.players[0];
			return Object.values(firstPlayer)[0];
		}
		return null;
	}
</script>

{#if (allGroups && Object.keys(allGroups).length > 0) || (otherGroups && Object.keys(otherGroups).length > 0)}
	<div class="group-selector mx-4 my-4 flex max-h-[85vh] min-h-[500px] flex-col px-6 py-4 sm:px-16 sm:py-8 scrollable mx-auto mb-6 w-full max-w-5xl min-w-0 overflow-y-auto rounded-lg bg-gray-900 p-6 transition-all duration-300 sm:p-8">
		<div class="flex flex-col h-full">
			<div class="flex flex-col items-start justify-between gap-2 xs:flex-row xs:items-center xs:gap-0" transition:slide={{ duration: 300 }}>
				<div class="flex flex-wrap items-center">
					{#if selectedGroup && (allGroups[selectedGroup] || otherGroups?.[selectedGroup])}
						<div class="flex items-center mr-2">
							<div class="flex items-center justify-center w-8 h-8 mr-2 overflow-hidden bg-gray-800 rounded-full">
								<img src="/android-chrome-192x192.png" alt="{selectedGroup} logo" class="object-cover w-full h-full" />
							</div>
							<span class="font-medium text-white">{selectedGroup}</span>
						</div>
						<div class="flex flex-wrap items-center gap-2 mt-1 xs:mt-0">
							<span class="px-2 py-1 text-xs text-white bg-blue-900 rounded">HD</span>
							<div class="flex items-center">
								<div class="relative h-4 mr-1 w-7">
									<img src="/jp.svg" alt="Japanese" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
								</div>
								<span class="text-sm text-white">JP</span>
							</div>
							<div class="flex items-center">
								<div class="relative h-4 mr-1 w-7">
									<img src="/pl.svg" alt="Polish" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
								</div>
								<span class="text-sm text-white">PL</span>
							</div>
						</div>
					{:else}
						<span class="font-medium text-white">Dostępne inne grupy</span>
					{/if}
				</div>
				<div class="flex justify-end w-full gap-2 mt-2 xs:w-auto xs:mt-0">
					<!-- Player selection dropdown will appear here if a group is selected -->
					{#if selectedGroup && currentGroupPlayers && currentGroupPlayers.length > 0}
						<DropdownMenu.Root>
							<DropdownMenu.Trigger asChild let:builder>
								<Button variant="outline" size="sm" builders={[builder]} class="flex-1 xs:flex-none hover:cursor-pointer hover:bg-gray-600">
									{currentPlayerType ? preservePlayerName(currentPlayerType) : selectedGroup === 'lycoris.cafe' ? 'lycoris.cafe' : 'Wybierz player'}
								</Button>
							</DropdownMenu.Trigger>
							<DropdownMenu.Content class="overflow-y-auto text-white bg-gray-800 border-gray-700 max-h-48">
								{#each currentGroupPlayers as player}
									{@const playerType = Object.keys(player).find((key) => key !== 'quality' && key !== 'audio_language' && key !== 'subtitle_language')}
									{@const playerUrl = player[playerType]}
									{@const playerQuality = player.quality || 'HD'}
									{@const playerAudio = player.audio_language || 'jp'}
									{@const playerSubtitle = player.subtitle_language || 'pl'}
									<DropdownMenu.Item on:click={() => selectSpecificPlayer(playerType, playerUrl, selectedGroup)} class="cursor-pointer {currentPlayerType === playerType && selectedGroupUrl === playerUrl ? 'bg-gray-700' : ''}">
										<div class="flex flex-col">
											<span class="mb-1 font-medium transition-colors hover:text-blue-400">{preservePlayerName(playerType)}</span>
											<!-- Quality badges for individual players - using actual player data -->
											<div class="flex items-center gap-1 sm:gap-2">
												<span class="rounded bg-blue-900 px-1 py-0.5 text-xs text-white sm:px-2 sm:py-1">{playerQuality}</span>
												<div class="flex items-center">
													<div class="relative w-5 h-3 mr-1 sm:h-4 sm:w-7">
														<img src="/{playerAudio}.svg" alt={playerAudio === 'jp' ? 'Japanese' : 'Chinese'} class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
													</div>
													<span class="text-xs text-white">{playerAudio.toUpperCase()}</span>
												</div>
												<div class="flex items-center">
													<div class="relative w-5 h-3 mr-1 sm:h-4 sm:w-7">
														<img src="/{playerSubtitle}.svg" alt={playerSubtitle === 'pl' ? 'Polish' : 'English'} class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
													</div>
													<span class="text-xs text-white">{playerSubtitle.toUpperCase()}</span>
												</div>
											</div>
										</div>
									</DropdownMenu.Item>
								{/each}
							</DropdownMenu.Content>
						</DropdownMenu.Root>
					{/if}
					<Button variant="outline" size="sm" on:click={toggleCollapse} class="flex-1 xs:flex-none hover:cursor-pointer hover:bg-gray-600">Pokaż grupy</Button>
				</div>
			</div>
		{:else}
			<div transition:slide={{ duration: 300 }} class="flex flex-col h-full">
				<!-- Header Island -->
				<div class="flex-shrink-0 min-w-0 p-4 mb-2 overflow-hidden bg-gray-700 rounded-lg shadow-lg sm:p-6">
					<div class="flex flex-col items-center justify-between gap-3 {isAdmin && !selectedGroup ? 'sm:mb-2 sm:flex-row' : 'sm:flex-col'} min-w-0 sm:items-center">
						{#if fullscreenMode}
							<h2 class="text-xl font-bold text-white sm:text-2xl {isAdmin && !selectedGroup ? '' : 'text-center'}">Wybierz grupę tłumaczeniową</h2>
						{:else}
							<!-- Always show title for non-fullscreen mode -->
							<!-- On desktop (lg+), show the title normally -->
							<h2 class="hidden text-lg font-bold text-center text-white sm:text-xl lg:block">Wybierz grupę tłumaczeniową</h2>
							<!-- On mobile/medium screens (md and below), show title in background area where edit button would be -->
							<div class="flex justify-center w-full px-2 py-1 lg:hidden">
								<h2 class="-mt-2 -mb-6 text-lg font-bold text-center text-white sm:text-xl">Wybierz grupę tłumaczeniową</h2>
							</div>
						{/if}
						{#if (isAdmin && !selectedGroup) || !fullscreenMode}
							<div class="flex justify-end gap-2">
								{#if isAdmin && !selectedGroup}
									<Button variant={editMode ? 'default' : 'outline'} size="sm" on:click={toggleEditMode} class="hidden hover:cursor-pointer hover:bg-gray-600 lg:block">
										{editMode ? 'Zakończ edycję' : 'Edytuj grupy'}
									</Button>
								{/if}
							</div>
						{/if}
					</div>
				</div>

				<div class="{fullscreenMode ? 'flex flex-1 flex-col py-4' : 'rounded-lg bg-gray-800'} min-w-0 flex-1 overflow-x-hidden">
					<!-- Mobile card view for xs screens -->
					<div class="space-y-3 sm:hidden {fullscreenMode ? 'space-y-4' : ''} p-2">
						{#each getSortedGroupEntries(hasSecondarySource ? allGroups : otherGroups || {}) as [groupName, groupData], index}
							<div role="button" tabindex="0" class="group overflow-hidden rounded-md transition-all duration-300 {selectedGroup === groupName ? 'bg-[#8ec3f4]/80' : 'bg-gray-700 hover:bg-gray-600'}" on:click={() => !editMode && selectGroup(groupName)} on:keydown={(e) => e.key === 'Enter' && !editMode && selectGroup(groupName)}>
								<div class="p-3 border-b border-gray-800 sm:p-4">
									<div class="flex items-center">
										<div class="flex items-center justify-center w-8 h-8 mr-2 overflow-hidden bg-gray-800 rounded-full sm:mr-3 sm:h-10 sm:w-10">
											<img src="/android-chrome-192x192.png" alt="{groupName} logo" class="object-cover w-full h-full" />
										</div>
										<div class="flex-1">
											<span class="text-base font-medium text-white sm:text-lg">{groupName}</span>
										</div>

										<!-- Actions for mobile -->
										{#if editMode && isAdmin && !selectedGroup}
											<div class="flex gap-1 ml-auto">
												{#if groupName !== 'lycoris.cafe'}
													<Button
														variant="outline"
														size="sm"
														on:click={(e) => {
															e.stopPropagation();
															openAddPlayerDialog(groupName);
														}}
														class="px-2 text-xs hover:cursor-pointer"
													>
														Dodaj
													</Button>
												{/if}
											</div>
										{:else if groupName === 'lycoris.cafe'}
											<!-- Keep button for lycoris.cafe -->
											<Button
												variant={selectedGroup === groupName ? 'default' : 'outline'}
												size="sm"
												class="ml-auto hover:cursor-pointer {selectedGroup === groupName ? 'bg-blue-900 text-white hover:bg-blue-800' : ''}"
												on:click={(e) => {
													e.stopPropagation();
													selectGroup(groupName);
												}}
												aria-label="Wybierz grupę {groupName}"
												aria-pressed={selectedGroup === groupName}
											>
												{selectedGroup === groupName ? 'Wybrano' : 'Wybierz'}
											</Button>
										{/if}
									</div>

									<!-- Mobile info row - only for lycoris.cafe -->
									{#if groupName === 'lycoris.cafe'}
										<div class="flex items-center gap-1 mt-1 sm:mt-2 sm:gap-2">
											<span class="rounded bg-blue-900 px-1 py-0.5 text-xs text-white sm:px-2 sm:py-1">HD</span>
											<div class="flex items-center">
												<div class="relative w-5 h-3 mr-1 sm:h-4 sm:w-7">
													<img src="/jp.svg" alt="Japanese" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
												</div>
												<span class="text-xs text-white">JP</span>
											</div>
											<div class="flex items-center">
												<div class="relative w-5 h-3 mr-1 sm:h-4 sm:w-7">
													<img src="/pl.svg" alt="Polish" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
												</div>
												<span class="text-xs text-white">PL</span>
											</div>
										</div>
									{/if}
								</div>

								<!-- Players List for non-lycoris.cafe groups -->
								{#if groupName !== 'lycoris.cafe' && groupData.players && groupData.players.length > 0}
									<div>
										{#each groupData.players as player, playerIndex}
											{@const playerType = Object.keys(player).find((key) => key !== 'quality' && key !== 'audio_language' && key !== 'subtitle_language')}
											{@const playerUrl = player[playerType]}
											{@const playerQuality = player.quality || 'HD'}
											{@const playerAudio = player.audio_language || 'jp'}
											{@const playerSubtitle = player.subtitle_language || 'pl'}
											<div role="button" tabindex="0" class="hover:bg-gray-550 cursor-pointer border-b border-black bg-gray-600 p-3 pl-6 transition-colors {selectedGroup === groupName && selectedGroupUrl === playerUrl ? 'bg-[#8ec3f4]/80' : ''}" on:click={() => !editMode && selectSpecificPlayer(playerType, playerUrl, groupName)} on:keydown={(e) => e.key === 'Enter' && !editMode && selectSpecificPlayer(playerType, playerUrl, groupName)}>
												<div class="flex items-start justify-between">
													<div class="flex-1 pr-2">
														<div class="mb-1 font-medium text-white">{preservePlayerName(playerType)}</div>
														<!-- Quality badges for individual players - using actual player data -->
														<div class="flex items-center gap-1 sm:gap-2">
															<span class="rounded bg-blue-900 px-1 py-0.5 text-xs text-white sm:px-2 sm:py-1">{playerQuality}</span>
															<div class="flex items-center">
																<div class="relative w-5 h-3 mr-1 sm:h-4 sm:w-7">
																	<img src="/{playerAudio}.svg" alt={playerAudio === 'jp' ? 'Japanese' : 'Chinese'} class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
																</div>
																<span class="text-xs text-white">{playerAudio.toUpperCase()}</span>
															</div>
															<div class="flex items-center">
																<div class="relative w-5 h-3 mr-1 sm:h-4 sm:w-7">
																	<img src="/{playerSubtitle}.svg" alt={playerSubtitle === 'pl' ? 'Polish' : 'English'} class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
																</div>
																<span class="text-xs text-white">{playerSubtitle.toUpperCase()}</span>
															</div>
														</div>
													</div>
													{#if editMode && isAdmin && !selectedGroup}
														<Button
															variant="destructive"
															size="sm"
															on:click={(e) => {
																e.stopPropagation();
																confirmDeletePlayer(groupName, playerUrl);
															}}
															class="px-2 text-xs hover:cursor-pointer"
														>
															Usuń
														</Button>
													{:else if !editMode}
														<Button
															variant={selectedGroup === groupName && selectedGroupUrl === playerUrl ? 'default' : 'outline'}
															size="sm"
															class="hover:cursor-pointer {selectedGroup === groupName && selectedGroupUrl === playerUrl ? 'bg-blue-900 text-white hover:bg-blue-800' : ''}"
															on:click={(e) => {
																e.stopPropagation();
																selectSpecificPlayer(playerType, playerUrl, groupName);
															}}
														>
															{selectedGroup === groupName && selectedGroupUrl === playerUrl ? 'Wybrano' : 'Wybierz'}
														</Button>
													{/if}
												</div>
											</div>
										{/each}
									</div>
								{/if}
							</div>
						{/each}
					</div>

					<!-- Desktop table view -->
					<div class="hidden min-w-0 p-2 sm:block">
						<table class="w-full min-w-0 overflow-hidden border-collapse rounded-lg">
							<thead class="sticky top-0 z-10 bg-black">
								<tr>
									<th class="p-3 font-medium text-left text-gray-400">Grupa</th>
									<th class="p-3 font-medium text-center text-gray-400">Jakość</th>
									<th class="p-3 font-medium text-center text-gray-400">Audio</th>
									<th class="p-3 font-medium text-center text-gray-400">Napisy</th>
									<th class="p-3 font-medium text-right text-gray-400">
										{#if editMode && isAdmin}
											Akcje
										{/if}
									</th>
								</tr>
							</thead>
							<tbody>
								{#each getSortedGroupEntries(hasSecondarySource ? allGroups : otherGroups || {}) as [groupName, groupData], index}
									<tr class="group transition-all duration-300 hover:cursor-pointer {selectedGroup === groupName ? 'bg-[#8ec3f4]/80' : 'bg-gray-700 hover:bg-gray-600'}" on:click={() => !editMode && selectGroup(groupName)}>
										<!-- Group Logo and Name -->
										<td class="p-3 border-b border-gray-800">
											<div class="flex items-center">
												<div class="flex items-center justify-center w-10 h-10 mr-3 overflow-hidden bg-gray-800 rounded-full">
													<img src="/android-chrome-192x192.png" alt="{groupName} logo" class="object-cover w-full h-full" />
												</div>
												<span class="text-lg font-medium text-white">{groupName}</span>
											</div>
										</td>

										<!-- Quality -->
										<td class="p-3 text-center border-b border-gray-800">
											{#if groupName === 'lycoris.cafe'}
												<span class="inline-block px-2 py-1 text-xs text-white bg-blue-900 rounded">HD</span>
											{/if}
										</td>

										<!-- Audio Language -->
										<td class="p-3 text-center border-b border-gray-800">
											{#if groupName === 'lycoris.cafe'}
												<div class="flex items-center justify-center">
													<div class="relative h-5 mr-1 w-9">
														<img src="/jp.svg" alt="Japanese" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
													</div>
													<span class="text-sm text-white">JP</span>
												</div>
											{/if}
										</td>

										<!-- Subtitle Language -->
										<td class="p-3 text-center border-b border-gray-800">
											{#if groupName === 'lycoris.cafe'}
												<div class="flex items-center justify-center">
													<div class="relative h-5 mr-1 w-9">
														<img src="/pl.svg" alt="Polish" class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
													</div>
													<span class="text-sm text-white">PL</span>
												</div>
											{/if}
										</td>

										<!-- Actions -->
										<td class="p-3 text-right border-b border-gray-800">
											{#if editMode && isAdmin && !selectedGroup}
												<div class="flex justify-end gap-2">
													{#if groupName !== 'lycoris.cafe'}
														<Button
															variant="outline"
															size="sm"
															on:click={(e) => {
																e.stopPropagation();
																openAddPlayerDialog(groupName);
															}}
															class="hover:cursor-pointer"
														>
															Dodaj player
														</Button>
													{/if}
												</div>
											{:else if groupName === 'lycoris.cafe'}
												<!-- Keep button for lycoris.cafe -->
												<Button
													variant={selectedGroup === groupName ? 'default' : 'outline'}
													size="sm"
													class="hover:cursor-pointer {selectedGroup === groupName ? 'bg-blue-900 text-white hover:bg-blue-800' : ''}"
													on:click={(e) => {
														e.stopPropagation();
														selectGroup(groupName);
													}}
													aria-label="Wybierz grupę {groupName}"
													aria-pressed={selectedGroup === groupName}
												>
													{selectedGroup === groupName ? 'Wybrano' : 'Wybierz'}
												</Button>
											{/if}
										</td>
									</tr>

									<!-- Show players for non-lycoris.cafe groups -->
									{#if groupName !== 'lycoris.cafe' && groupData.players && groupData.players.length > 0}
										{#each groupData.players as player, playerIndex}
											{@const playerType = Object.keys(player).find((key) => key !== 'quality' && key !== 'audio_language' && key !== 'subtitle_language')}
											{@const playerUrl = player[playerType]}
											{@const playerQuality = player.quality || 'HD'}
											{@const playerAudio = player.audio_language || 'jp'}
											{@const playerSubtitle = player.subtitle_language || 'pl'}
											<tr class="hover:bg-gray-550 cursor-pointer bg-gray-600 transition-colors {selectedGroup === groupName && selectedGroupUrl === playerUrl ? 'bg-[#8ec3f4]/80' : ''}" on:click={() => !editMode && selectSpecificPlayer(playerType, playerUrl, groupName)}>
												<!-- Player Name -->
												<td class="p-3 pl-16 border-b border-black">
													<div class="font-medium text-white">{preservePlayerName(playerType)}</div>
												</td>
												<!-- Quality Badge -->
												<td class="p-3 text-center border-b border-black">
													<span class="rounded bg-blue-900 px-1 py-0.5 text-xs text-white sm:px-2 sm:py-1">{playerQuality}</span>
												</td>
												<!-- Audio Badge -->
												<td class="p-3 text-center border-b border-black">
													<div class="flex items-center justify-center">
														<div class="relative w-5 h-3 mr-1 sm:h-4 sm:w-7">
															<img src="/{playerAudio}.svg" alt={playerAudio === 'jp' ? 'Japanese' : 'Chinese'} class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
														</div>
														<span class="text-xs text-white">{playerAudio.toUpperCase()}</span>
													</div>
												</td>
												<!-- Subtitle Badge -->
												<td class="p-3 text-center border-b border-black">
													<div class="flex items-center justify-center">
														<div class="relative w-5 h-3 mr-1 sm:h-4 sm:w-7">
															<img src="/{playerSubtitle}.svg" alt={playerSubtitle === 'pl' ? 'Polish' : 'English'} class="object-cover w-full h-full" onerror="this.src='/android-chrome-192x192.png'; this.onerror=null;" />
														</div>
														<span class="text-xs text-white">{playerSubtitle.toUpperCase()}</span>
													</div>
												</td>
												<td class="p-3 text-right border-b border-black">
													{#if editMode && isAdmin && !selectedGroup}
														<Button
															variant="destructive"
															size="sm"
															on:click={(e) => {
																e.stopPropagation();
																confirmDeletePlayer(groupName, playerUrl);
															}}
															class="hover:cursor-pointer"
														>
															Usuń
														</Button>
													{:else if !editMode}
														<Button
															variant={selectedGroup === groupName && selectedGroupUrl === playerUrl ? 'default' : 'outline'}
															size="sm"
															class="hover:cursor-pointer {selectedGroup === groupName && selectedGroupUrl === playerUrl ? 'bg-blue-900 text-white hover:bg-blue-800' : ''}"
															on:click={(e) => {
																e.stopPropagation();
																selectSpecificPlayer(playerType, playerUrl, groupName);
															}}
														>
															{selectedGroup === groupName && selectedGroupUrl === playerUrl ? 'Wybrano' : 'Wybierz'}
														</Button>
													{/if}
												</td>
											</tr>
										{/each}
									{/if}
								{/each}
							</tbody>
						</table>
					</div>
				</div>

				{#if editMode && isAdmin && !selectedGroup}
					<div class="flex-shrink-0 pt-2 pb-1 mt-4 bg-gray-800 border-t border-gray-700">
						<p class="mb-2 text-sm text-gray-400">Wybierz grupę z listy zatwierdzonych grup, a następnie dodaj link do playera.</p>
						<div class="flex justify-center">
							<Button variant="outline" class="w-full xs:w-auto hover:cursor-pointer" on:click={openAddGroupDialog}>Dodaj nową grupę z playerem</Button>
						</div>
					</div>
				{/if}
			</div>
		{/if}
	</div>
{/if}

<!-- Delete Confirmation Dialog -->
<Dialog bind:open={showDeleteConfirmDialog}>
	<DialogContent class="mx-auto w-[95vw] max-w-md border-gray-700 bg-gray-900 p-4 text-white sm:p-6">
		<DialogHeader>
			<DialogTitle>Potwierdź usunięcie</DialogTitle>
		</DialogHeader>
		<div class="py-4">
			<p>Czy na pewno chcesz usunąć tego playera?</p>
			<p class="mt-2 text-sm text-gray-400">Ta operacja jest nieodwracalna.</p>
			<p class="mt-2 text-xs text-gray-500">Grupa: {playerToDelete.groupName}</p>
		</div>
		<DialogFooter class="flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
			<Button variant="outline" on:click={() => (showDeleteConfirmDialog = false)} class="w-full hover:cursor-pointer sm:w-auto">Anuluj</Button>
			<Button variant="destructive" on:click={executeDelete} class="w-full hover:cursor-pointer sm:w-auto">Usuń</Button>
		</DialogFooter>
	</DialogContent>
</Dialog>

<!-- Add Player Dialog -->
<Dialog bind:open={showAddPlayerDialog}>
	<DialogContent class="mx-auto w-[95vw] max-w-md border-gray-700 bg-gray-900 p-4 text-white sm:p-6">
		<DialogHeader>
			<DialogTitle>Dodaj nowy player</DialogTitle>
			<p class="text-sm text-gray-400">Dodaj player dla grupy: {selectedGroupForNewPlayer}</p>
		</DialogHeader>
		<div class="py-4 space-y-4">
			<div>
				<label for="playerUrl" class="block mb-1 text-sm font-medium">URL playera</label>
				<input id="playerUrl" type="text" bind:value={newPlayerUrl} class="w-full p-3 text-white bg-gray-800 border border-gray-700 rounded-md" placeholder="https://..." on:keydown={(e) => e.key === 'Enter' && addNewPlayer()} />
				<p class="mt-1 text-xs text-gray-400">URL zostanie automatycznie rozpoznany na podstawie listy zatwierdzonych playerów.</p>
			</div>

			<div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
				<div>
					<label for="quality" class="block mb-1 text-sm font-medium">Jakość</label>
					<select id="quality" bind:value={selectedQuality} class="w-full p-3 text-white bg-gray-800 border border-gray-700 rounded-md">
						<option value="1080">1080p</option>
						<option value="720">720p</option>
						<option value="480">480p</option>
					</select>
				</div>

				<div>
					<label for="audioLanguage" class="block mb-1 text-sm font-medium">Audio</label>
					<select id="audioLanguage" bind:value={selectedAudioLanguage} class="w-full p-3 text-white bg-gray-800 border border-gray-700 rounded-md">
						<option value="jp">Japoński</option>
						<option value="cn">Chiński</option>
					</select>
				</div>

				<div>
					<label for="subtitleLanguage" class="block mb-1 text-sm font-medium">Napisy</label>
					<select id="subtitleLanguage" bind:value={selectedSubtitleLanguage} class="w-full p-3 text-white bg-gray-800 border border-gray-700 rounded-md">
						<option value="pl">Polski</option>
						<option value="eng">Angielski</option>
					</select>
				</div>
			</div>
		</div>
		<DialogFooter class="flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
			<Button variant="outline" on:click={() => (showAddPlayerDialog = false)} class="w-full hover:cursor-pointer sm:w-auto">Anuluj</Button>
			<Button variant="default" on:click={addNewPlayer} class="w-full hover:cursor-pointer sm:w-auto">Dodaj</Button>
		</DialogFooter>
	</DialogContent>
</Dialog>

<!-- Add Group Dialog -->
<Dialog bind:open={showAddGroupDialog}>
	<DialogContent class="mx-auto w-[95vw] max-w-md border-gray-700 bg-gray-900 p-4 text-white sm:p-6">
		<DialogHeader>
			<DialogTitle>Dodaj nową grupę z playerem</DialogTitle>
			<p class="text-sm text-gray-400">Wybierz grupę z listy zatwierdzonych grup i dodaj link do playera.</p>
		</DialogHeader>
		<div class="py-4 space-y-4">
			<div>
				<label for="groupSelect" class="block mb-1 text-sm font-medium">Grupa tłumaczeniowa</label>
				<select id="groupSelect" bind:value={selectedGroupFromList} class="w-full p-3 text-white bg-gray-800 border border-gray-700 rounded-md">
					<option value="">Wybierz grupę...</option>
					{#each availableGroups as group}
						<option value={group.name}>{group.name}</option>
					{/each}
				</select>
			</div>
			<div>
				<label for="groupPlayerUrl" class="block mb-1 text-sm font-medium">URL playera</label>
				<input id="groupPlayerUrl" type="text" bind:value={newPlayerUrl} class="w-full p-3 text-white bg-gray-800 border border-gray-700 rounded-md" placeholder="https://..." on:keydown={(e) => e.key === 'Enter' && addGroupWithPlayer()} />
				<p class="mt-1 text-xs text-gray-400">URL zostanie automatycznie rozpoznany na podstawie listy zatwierdzonych playerów.</p>
			</div>

			<div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
				<div>
					<label for="groupQuality" class="block mb-1 text-sm font-medium">Jakość</label>
					<select id="groupQuality" bind:value={selectedQuality} class="w-full p-3 text-white bg-gray-800 border border-gray-700 rounded-md">
						<option value="1080">1080p</option>
						<option value="720">720p</option>
						<option value="480">480p</option>
					</select>
				</div>

				<div>
					<label for="groupAudioLanguage" class="block mb-1 text-sm font-medium">Audio</label>
					<select id="groupAudioLanguage" bind:value={selectedAudioLanguage} class="w-full p-3 text-white bg-gray-800 border border-gray-700 rounded-md">
						<option value="jp">Japoński</option>
						<option value="cn">Chiński</option>
					</select>
				</div>

				<div>
					<label for="groupSubtitleLanguage" class="block mb-1 text-sm font-medium">Napisy</label>
					<select id="groupSubtitleLanguage" bind:value={selectedSubtitleLanguage} class="w-full p-3 text-white bg-gray-800 border border-gray-700 rounded-md">
						<option value="pl">Polski</option>
						<option value="eng">Angielski</option>
					</select>
				</div>
			</div>
		</div>
		<DialogFooter class="flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
			<Button variant="outline" on:click={() => (showAddGroupDialog = false)} class="w-full hover:cursor-pointer sm:w-auto">Anuluj</Button>
			<Button variant="default" on:click={addGroupWithPlayer} class="w-full hover:cursor-pointer sm:w-auto">Dodaj</Button>
		</DialogFooter>
	</DialogContent>
</Dialog>

<style>
	.group-selector {
		transition: all 0.3s ease;
		/* Ensure proper width constraints on all screen sizes */
		box-sizing: border-box;
		/* Enable scrolling on all devices */
		overflow-x: hidden;
		overflow-y: auto;
		/* Improve scrolling on touch devices */
		-webkit-overflow-scrolling: touch;
		/* Ensure scrolling works on all devices */
		overscroll-behavior: contain;
		/* Unified scrolling - this is the only container that should scroll */
		display: flex;
		flex-direction: column;
	}

	.scrollable {
		scrollbar-width: thin;
		scrollbar-color: #4b5563 #374151;
		/* Improve scrolling on touch devices */
		-webkit-overflow-scrolling: touch;
		/* Ensure scrolling works on all devices */
		overscroll-behavior: contain;
	}

	.scrollable::-webkit-scrollbar {
		width: 6px;
	}

	.scrollable::-webkit-scrollbar-track {
		background: #374151;
		border-radius: 3px;
	}

	.scrollable::-webkit-scrollbar-thumb {
		background: #4b5563;
		border-radius: 3px;
	}

	.scrollable::-webkit-scrollbar-thumb:hover {
		background: #6b7280;
	}

	/* Apply scrollbar styling to group-selector itself */
	.group-selector::-webkit-scrollbar {
		width: 6px;
	}

	.group-selector::-webkit-scrollbar-track {
		background: #374151;
		border-radius: 3px;
	}

	.group-selector::-webkit-scrollbar-thumb {
		background: #4b5563;
		border-radius: 3px;
	}

	.group-selector::-webkit-scrollbar-thumb:hover {
		background: #6b7280;
	}

	.group-selector {
		scrollbar-width: thin;
		scrollbar-color: #4b5563 #374151;
	}

	/* Ensure minimum heights for proper unified scrolling on all devices */
	@media (max-width: 639px) {
		.group-selector {
			min-height: 300px;
			max-height: 75vh;
		}
	}

	@media (min-width: 640px) and (max-width: 1023px) {
		.group-selector {
			min-height: 350px;
			max-height: 80vh;
		}
	}

	@media (min-width: 1024px) {
		.group-selector {
			min-height: 400px;
			max-height: 85vh;
		}
	}

	/* Custom brighter gray colors for sub-items */
	.bg-gray-550 {
		background-color: #6b7280;
	}

	.hover\:bg-gray-550:hover {
		background-color: #6b7280;
	}

	/* Ensure touch targets are large enough on mobile */
	@media (max-width: 480px) {
		[role='button'] {
			min-height: 40px;
		}

		input {
			min-height: 40px;
		}
	}

	/* Additional scrolling improvements for mobile */
	@media (max-width: 767px) {
		.group-selector {
			/* Ensure momentum scrolling on iOS */
			-webkit-overflow-scrolling: touch;
			/* Prevent scroll chaining */
			overscroll-behavior-y: contain;
		}
	}
</style>
